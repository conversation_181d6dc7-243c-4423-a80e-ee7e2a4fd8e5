server:
  port: 8803
spring:
  main:
    allow-bean-definition-overriding: true
  messages:
    basename: i18n/messages
    encoding: utf-8
  datasource:
    url: jdbc:mysql://${BIZ_MYSQL_HOST}:${BIZ_MYSQL_PORT}/ghos_pvw?autoReconnect=true&characterEncoding=utf8&allowMultiQueries=true&serverTimezone=Asia/Shanghai&zeroDateTimeBehavior=convertToNull
    username: ghos_pvw
    password: lxxjj(LLtq07
    #    driver-class-name: com.p6spy.engine.spy.P6SpyDriver
    driver-class-name: com.mysql.cj.jdbc.Driver
    # hikari 连接池配置
    # Reference: https://github.com/brettwooldridge/HikariCP#gear-configuration-knobs-baby
    hikari:
      # 连接池最大值
      # connections = ((core_count * 2) + Effective_spindle_count)，默认值: 10
      maximum-pool-size: ${BIZ_HK_POOL_SIZE_MAX:100}
      # 连接池超时时间，最低连接超时时间为 250 ms。 默认值：30000 ms，应该大于 mysql connect_timeout
      connection-timeout: ${BIZ_HK_POOL_CON_TIMEOUT:30000}
      # 最小空闲连接数，默认值：与 maximum-pool-size 相同
      minimum-idle: ${BIZ_HK_POOL_IDLE_MIN:80}
      # 连接空闲的最长时间，minimum-idle 小于 maximum-pool-size 生效。 0 表示永远不会从池中删除空闲连接。允许的最小值为 10000 ms。 默认值：600000 ms
      idle-timeout: ${BIZ_HK_POOL_IDLE_TIMEOUT:600000}
      # 连接的最长生命周期，0 表示没有最大生命周期，此值不能大于数据库、代理的连接最大超时时间。最小值为 30000 ms。 默认值：1800000 ms
      max-lifetime: ${BIZ_HK_POOL_IDLE_MAX_LIFE:1800000}
      # 连接池名称
      pool-name: PHS-HIKARIPOOL
      # 验证连接池 SQL
      connection-test-query: SELECT 1
      # 默认注入 Beans 用于监控
      register-mbeans: true
      # 连接泄露检测时间, 0 表示禁止检测(默认), 如果设置则必须大于等于 2 秒, 如果未设置则默认为5分钟
      leak-detection-threshold: ${BIZ_HK_POOL_LEAK:300000}
  servlet:
    multipart:
      enabled: true
      max-file-size: 1GB
      max-request-size: 1GB
  mybatis:
    mapper-locations: classpath:mapper/*/*.xml
  data:
    mongodb:
      uri: ${BIZ_MONGO_URL}
      database: chatbot_${spring.profiles.active}
      #  支持动态切库
      dynamic-enable: true
      #  跨集群的时候再打开，同一个port下不用打开
      multi-enable: false
  redis:
    cluster:
      nodes: ${BIZ_REDIS_NODES:clustercfg.halpilot-test-redis.pczaql.memorydb.us-east-1.amazonaws.com:6379}
    ssl: ${BIZ_REDIS_SSL:false}
    connect-timeout: 20000
    password: ${BIZ_REDIS_PASSWORD}
  # 缓存相关
  cache:
    # 缓存存储类型
    type: redis
  # Kafka configuration
  kafka:
    bootstrap-servers: ${BIZ_KAFKA_BOOTSTRAP_SERVERS:**************:9092,**************:9092,**************:9092}
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      batch-size: 16384
      buffer-memory: 33554432
      retries: 3
      properties:
        linger:
          ms: 5
    consumer:
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      auto-offset-reset: ${BIZ_KAFKA_CONSUMER_OFFSET_RESET:latest}
      enable-auto-commit: false
      auto-commit-interval: 100ms
      properties:
        session.timeout.ms: 30000
      group-id: ${BIZ_KAFKA_CONSUMER_GROUP:CHATBOT_CONSUMER_PROD}
    listener:
      ack-mode: manual_immediate
# 日志相关
logging:
  #等级((TRACE, DEBUG, INFO, WARN, ERROR, FATAL, OFF))
  level:
    org:
      apache:
        kafka: OFF

# ChatBot 相关配置，后续跟 ChatBot 相关的在改目录下创建
chatbot:
  address: ${CHAT_BOT_ADDRESS:https://smartchat-test-sg.evyd.tech/evyd-api/chatbot}
  # 依赖服务的开关
  enable-service-lists:
    # 短信服务
    sms: true
    # 邮件服务
    mail: true
  ai-platform:
    openai:
      api-key: ${OPEN_AI_API_KEY:***************************************************}
      chat-model-name: ${OPEN_AI_MODEL:gpt-4o}
      completion-model-name: ${OPEN_AI_MODEL_COMPLETION:gpt-4o}
      api-timeout: ${OPEN_AI_TIMEOUT:30}
      max-token: ${OPEN_AI_MAX_TOKEN:12000}
      trace-log-length: ${OPEN_AI_TRACE_LOG_LENGTH:1500}
  intent:
    search:
      prompt-system: "You are great ai"
      prompt-assistant: "The assistant content"
      prompt-user-prefix: "Please extract key worlds and user's intent by json(json key as keyWords and userIntent) form this sentence: "
    scene:
      prefix-conversation: ${CHAT_BOT_PREFIX_CONVERSATION:The following is a conversation between GNC assistant and user:}
      prefix-extract-intention: ${CHAT_BOT_PREFIX_EXTRACT_INTENTION:The following is a list of predefined user intentions:}
      suffix-extract-intention: ${CHAT_BOT_SUFFIX_EXTRACT_INTENTION:Considering this user's last words, classify his lastest intention into one of the above categories. Only output the category, without any explanation. If the intention is unclear, reply None.Intention:}
      base-score: ${CHAT_BOT_INTENT_BASE_SCORE:0.7}
  im-platform:
    send-bird:
      api-host: ${IM_SEND_BIRD_API_HOST:https://api-51C73DF8-FE52-4BDC-8736-AE017EBA9EBA.sendbird.com}
      api-token: ${IM_SEND_BIRD_API_TOKEN:bc5e22c914d76740fc0c11f79cc2211291fa97ad}
      app-id: ${IM_SEND_BIRD_APP_ID:51C73DF8-FE52-4BDC-8736-AE017EBA9EBA}
      bot-user-id: ${IM_SEND_BIRD_BOT_USER_ID:BOT_1}
      callback-token: ${IM_SEND_BIRD_CALLBACK_TOKEN:ck-1af52c20-a1d2-47bc-af91-35ba77471330}
      callback-url: ${IM_SEND_BIRD_CALLBACK_URL:https://chatbot-test-sg.evydhealth.com/evyd-api/chatbot/open/im/bot-callback/send-bird/ck-1af52c20-a1d2-47bc-af91-35ba77471330}
      super-user-id: ${IM_SEND_BIRD_SUPER_USER_ID:ADMIN_1}
  jwt:
    issuer-name: ${CHAT_BOT_JWT_ISSUER_NAME:EVYD}
    subject-name: ${CHAT_BOT_JWT_SUBJECT_NAME:CHAT_BOT_USER}
    secret-key: ${CHAT_BOT_JWT_SECRET_KEY:$2a$10$pBSTu10ChbKHbLDOEBcnROmf28RVXME1bpypKQoJMZ9lLXocPVuPK}
    validate-period: ${CHAT_BOT_JWT_VALIDATE_PERIOD:10}
  bot-info:
    name: ${CHAT_BOT_NAME:REGGIE}
    gender: ${CHAT_BOT_GENDER:Male}
    avatar-url: ${CHAT_BOT_AVATAR_URL:/resource/img/user/chatbot-avatar.png}
  debug:
    flag: ${CHAT_BOT_DEBUG_FLAG:false}
  cache:
    question-flag: ${CHAT_BOT_CACHE_QUESTION_FLAG:false}
    question-polish-flag: ${CHAT_BOT_CACHE_QUESTION_POLISH_FLAG:false}
  sys:
    switch-build-user-label: ${CHAT_BOT_SYS_SWITCH_BUILD_USER_LABEL:2}
  oss:
    enabled: ${BIZ_OSS_ENABLED:true}
    business: ${BIZ_OSS_BUSINESS:file}
    app-id: ${BIZ_OSS_APP_ID:hermes}
    app-secret: ${BIZ_OSS_APP_SECRET:e7d666294d2cf2e3cd01}
  proxy:
    switch: ${BIZ_PROXY_SWITCH:true}
    host: ${BIZ_PROXY_HOST:http://*************}
    port: ${BIZ_PROXY_PORT:80}
    time-out-ms: ${BIZ_PROXY_TIME_OUT_MS:60000}
  guardrails:
    host: ${CHAT-GUARDRAILS-HOST:http://evyd-chatbot-chatbot-guardrails}
  mq:
    kafka:
      topics:
        hsd-order: ${BIZ_KAFKA_TOPIC_HSD_ORDER:appointment_paid_ai_process}
        hermes-chatbot-sendmsg: ${BIZ_KAFKA_TOPIC_HERMES_CHATBOT_SENDMSG:HERMES_CHATBOT_SENDMSG_PROD}
  evo-mind:
    api-host: ${EVO_MIND_API_HOST:http://0.0.0.0:8000}
    stream-batch-size: ${BIZ_AI_STREAM_BATCH_SIZE:100}
  hm-backend-account-assist:
    api-host: ${EVYD_HM_BACKEND_ACCOUT_ASSIST:http://evyd-hm-backend-account-assist }

### Feign 相关 ###
feign:
  compression:
    # 配置请求GZIP压缩
    request:
      enabled: true
      # 配置压缩支持的MIME TYPE
      mime-types: text/xml,application/xml,application/json
      # 配置压缩数据大小的下限
      min-request-size: 2048
    # 配置响应GZIP压缩
    response:
      enabled: true
  # 采用 apache的 okhttp 作为 http访问
  okhttp:
    enabled: true
  # feign 客户端配置
  client:
    config:
      # 默认配置 -> 可单独指定 feignName
      default:
        # 链接超时时间
        connectTimeout: 600000
        # 读取超时时间
        readTimeout: 600000
        # 日志等级 (NONE, BASIC, HEADERS, FULL)
        loggerLevel: HEADERS

# 开启GZip压缩
service:
  compression:
    enable: true

### 分页相关 ###
pagehelper:
  helper-dialect: mysql
  #  auto-dialect: true
  reasonable: true
  support-methods-arguments: true

#向量数据库
weaviate:
  host: ${WEAVIATE_HOST:***********:8080}
  scheme: ${WEAVIATE_SCHEME:HTTP}
  product-query-threshold: ${WEAVIATE_PRODUCT_QUERY_THRESHOLD:0.9}

#es
es:
  host: ${ES_HOST:***********}
  port: ${ES_PORT:9200}
  schema: ${ES_SCHEMA:http}
  auth-type: ${ES_AUTH_TYPE:NONE}
  ca-path: ${ES_CA_PATH:/data/es/CA_CRT}
  finger-print: ${ES_FINGER_PRINT}
  username: ${ES_USERNAME}
  password: ${ES_PASSWORD}
  product:
    index-alias-name: ${ES_PRODUCT_INDEX_ALIAS_NAME:GNCProductsV20Test}
  knn:
    text-embedding-model-id: ${ES_TEXT_EMBEDDING_MODEL_ID:sentence-transformers__msmarco-minilm-l-12-v3}

#Azure
speech:
  enabled: ${BIZ_SPEECH_ENABLED:true}
  azure:
    host: ${AZURE_SPEECH_HOST:https://southeastasia.api.cognitive.microsoft.com}
    key: ${AZURE_SPEECH_KEY:8d68212467244ff7a2dbce011c11741c}
    region: ${AZURE_SPEECH_REGION:southeastasia}
    fast-stt-uri: ${AZURE_SPEECH_FAST_STT_URI:/speechtotext/transcriptions:transcribe?api-version=2024-05-15-preview}

# File upload
upload:
  speech:
    dir: ${UPLOAD_SPEECH_DIR:/home/<USER>/data/upload/speech}

### EVYD Common configuration ###
evyd:
  common-util:
    oss:
      server: ${BIZ_OSS_SERVER:http://evyd-health-manage-evyd-hm-oss/hm-oss}
      appId: ${BIZ_OSS_APP_ID:hermes}
      appSecret: ${BIZ_OSS_APP_SECRET:e7d666294d2cf2e3cd01}
    enable:
      stongman: false
  micro-service:
    hosts:
      health-agent: ${HEALTH_AGENT:http://smartchat-health-agent}


mock:
  cookie:
    internal: nginx_proxy_session_evyd=-kVMYBwod6CpelTsq36tpg..|**********|KRItKSYlGVb2CYQdTDSNLW3Y4JU.; web_nginx_info_evyd=077PZIvc+km7NgNpL+FXa1m2touBWBarj69hn2qDfZDMHIcN+ZvbQdzkfX1ccLNb
  app:
    ai-name: ${BIZ_APP_AI_NAME:BruHealth AI}