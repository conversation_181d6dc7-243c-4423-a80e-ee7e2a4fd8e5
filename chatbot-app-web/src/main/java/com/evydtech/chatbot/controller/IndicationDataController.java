/**
 * IndicationDataController
 * 
 * 指标数据查询控制器
 * 提供患者指标数据的查询接口
 * 支持根据患者ID和指标key列表查询最新指标数据
 *
 * @author: <PERSON>
 * @date: 2025/7/29
 * @license: Copyright © 2012 - 2025 YiDuCloud.EVYD
 */
package com.evydtech.chatbot.controller;

import com.evydtech.chatbot.business.pojo.qo.IndicationQueryQo;
import com.evydtech.chatbot.business.pojo.vo.IndicationQueryVo;
import com.evydtech.chatbot.common.domain.pojo.vo.Result;
import com.evydtech.chatbot.service.IndicationDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Slf4j
@RestController
public class IndicationDataController {

    @Autowired
    private IndicationDataService indicationDataService;

    /**
     * 查询患者最新指标数据
     * 
     * 根据患者ID和指标key列表查询最新的指标数据
     * 
     * @param request 查询请求参数
     * @return 包含指标数据的查询结果
     */
    @PostMapping("/indication/query-last")
    public Result<IndicationQueryVo> queryLast(@Valid @RequestBody IndicationQueryQo request) {
        log.info("查询患者指标数据，userId: {}, keys: {}", request.getUserId(), request.getKeys());
        try {
            IndicationQueryVo response = indicationDataService.queryLast(request);
            log.info("查询成功，返回记录数: {}", response.getList() != null ? response.getList().size() : 0);
            return Result.success(response);
        } catch (Exception e) {
            log.error("查询患者指标数据失败", e);
            return Result.fail("查询失败: " + e.getMessage());
        }
    }


}