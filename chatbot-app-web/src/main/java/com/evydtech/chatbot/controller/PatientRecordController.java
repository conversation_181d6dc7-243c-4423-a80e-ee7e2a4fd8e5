/**
 * PatientRecordController
 * 
 * 患者记录查询控制器
 * 提供患者记录查询的HTTP API接口
 * 支持通过SQL查询获取患者相关数据
 *
 * @author: Claude
 * @date: 2025/7/29
 * @license: Copyright © 2012 - 2025 YiDuCloud.EVYD
 */
package com.evydtech.chatbot.controller;

import com.evydtech.chatbot.common.domain.pojo.vo.Result;
import com.evydtech.chatbot.feign.domain.PatientRecordQueryVo;
import com.evydtech.chatbot.feign.service.PatientRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.Map;

@Slf4j
@RestController
public class PatientRecordController {

    @Autowired
    private PatientRecordService patientRecordService;

    /**
     * 查询患者记录
     * 
     * 通过SQL查询语句获取患者相关记录数据
     * 支持灵活的查询条件，返回结构化的查询结果
     * 
     * @param sqlQuery SQL查询语句
     * @return 包含查询结果的统一响应对象
     */
    @PostMapping("/patient/record/query")
    public Result<PatientRecordQueryVo<Map<String, Object>>> queryPatientRecords(@Valid @RequestBody SqlQueryRequest sqlQuery) {
        log.info("查询患者记录，SQL: {}", sqlQuery.getSql());
        try {
            PatientRecordQueryVo<Map<String, Object>> result = patientRecordService.queryPatientRecords(sqlQuery.getSql());
            log.info("查询成功，返回记录数: {}", result.getData() != null ? result.getData().size() : 0);
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询患者记录失败", e);
            return Result.fail("查询失败: " + e.getMessage());
        }
    }

    /**
     * SQL查询请求对象
     * 
     * 用于封装患者记录查询请求参数
     */
    public static class SqlQueryRequest {
        @NotBlank(message = "SQL查询语句不能为空")
        private String sql;

        public String getSql() {
            return sql;
        }

        public void setSql(String sql) {
            this.sql = sql;
        }
    }
}