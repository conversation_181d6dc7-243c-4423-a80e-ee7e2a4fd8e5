/**
 * IndicationDataServiceImpl
 * 
 * 指标数据服务实现类
 * 提供指标数据查询的业务逻辑实现
 * 通过调用外部服务或数据库获取患者指标数据
 *
 * @author: <PERSON>
 * @date: 2025/7/29
 * @license: Copyright © 2012 - 2025 YiDuCloud.EVYD
 */
package com.evydtech.chatbot.service.impl;

import com.evydtech.chatbot.business.pojo.qo.IndicationQueryQo;
import com.evydtech.chatbot.business.pojo.vo.IndicationQueryVo;
import com.evydtech.chatbot.business.pojo.vo.IndicationRecordVo;
import com.evydtech.chatbot.business.pojo.vo.IndicationDTO;
import com.evydtech.chatbot.service.IndicationDataService;
import com.evydtech.routines.dc.api.model.request.indication.IndicationPageQuery;
import com.evydtech.routines.dc.api.web.IndicationDataApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class IndicationDataServiceImpl implements IndicationDataService {

    @Autowired
    private IndicationDataApi indicationDataApi;

    @Override
    public IndicationQueryVo queryLast(IndicationQueryQo request) {
        log.info("开始查询患者指标数据，memberId: {}, keys: {}", request.getMemberId(), request.getKeys());
        
        // 构建请求对象
        IndicationPageQuery pageQuery = new IndicationPageQuery();
        pageQuery.setUserId(request.getMemberId());
        pageQuery.setKeys(request.getKeys());
        
        try {
            // 调用外部API
            var result = indicationDataApi.queryLast(pageQuery);
            
            // 转换结果
            IndicationQueryVo response = new IndicationQueryVo();
            List<IndicationRecordVo> records = new ArrayList<>();
            
            if (result != null && result.getList() != null) {
                int idCounter = 1;
                for (var compositeDTO : result.getList()) {
                    if (compositeDTO.getIndicationDTO() != null) {
                        var indicationDTO = compositeDTO.getIndicationDTO();
                        
                        IndicationRecordVo record = new IndicationRecordVo();
                        record.setId(idCounter++);
                        
                        IndicationDTO ourIndicationDTO = new IndicationDTO();
                        ourIndicationDTO.setKey(indicationDTO.getKey());
                        ourIndicationDTO.setValue(indicationDTO.getValue());
                        ourIndicationDTO.setValueType(indicationDTO.getValueType());
                        
                        // Handle extData - direct passthrough as string for downstream processing
                        ourIndicationDTO.setExtData(indicationDTO.getExtData());
                        
                        ourIndicationDTO.setRecordDateTime(indicationDTO.getRecordDateTime());
                        
                        record.setIndicationDTO(ourIndicationDTO);
                        records.add(record);
                    }
                }
                
                response.setTotal(result.getTotal());
                response.setList(records);
            } else {
                response.setTotal(0);
                response.setList(new ArrayList<>());
            }
            
            log.info("查询完成，返回记录数: {}", response.getTotal());
            return response;
            
        } catch (Exception e) {
            log.error("调用外部指标数据API失败", e);
            throw new RuntimeException("查询指标数据失败: " + e.getMessage(), e);
        }
    }
}