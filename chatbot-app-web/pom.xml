<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.evydtech.chatbot</groupId>
        <artifactId>chatbot</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>chatbot-app-web</artifactId>

    <properties>
        <evyd.chatbot.app.api.version>1.0.0-SNAPSHOT</evyd.chatbot.app.api.version>
        <evyd.chatbot.platform.api.version>1.0.0-SNAPSHOT</evyd.chatbot.platform.api.version>
    </properties>

    <!--Dependencies-->
    <dependencies>
        <!--EVYD-->
        <dependency>
            <groupId>com.evydtech.chatbot</groupId>
            <artifactId>chatbot-base</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>com.evydtech.chatbot</groupId>
            <artifactId>chatbot-app-api</artifactId>
            <version>${evyd.chatbot.app.api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.evydtech.chatbot</groupId>
            <artifactId>chatbot-platform-api</artifactId>
            <version>${evyd.chatbot.platform.api.version}</version>
        </dependency>

        <dependency>
            <groupId>jakarta.json</groupId>
            <artifactId>jakarta.json-api</artifactId>
            <version>${jakarta.json-api.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
            <version>2021.1</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
            <version>3.1.3</version>
        </dependency>
        <dependency>
            <groupId>com.evydtech.commonutil</groupId>
            <artifactId>nacos-json-config</artifactId>
            <version>1.0.0-EVYD-SNAPSHOT</version>
        </dependency>




    </dependencies>

    <!--Build-->
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <executable>true</executable>
                    <outputDirectory>${basedir}/../.docker</outputDirectory>
                    <includeSystemScope>true</includeSystemScope>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
