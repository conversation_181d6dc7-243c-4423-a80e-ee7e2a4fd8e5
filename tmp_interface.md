# 患者数据管理接口文档

## 文档信息
- **文档版本**: v1.0
- **创建日期**: 2025-07-30
- **最后更新**: 2025-07-30
- **维护团队**: 聊天机器人开发团队
- **文档状态**: 正式版

## 目录
- [简介](#简介)
- [接口概览](#接口概览)
- [通用规范](#通用规范)
  - [请求格式](#请求格式)
  - [响应格式](#响应格式)
  - [错误码规范](#错误码规范)
  - [参数校验规范](#参数校验规范)
- [IndicationDataController 指标数据控制器](#indicationdatacontroller-指标数据控制器)
  - [查询患者最新指标数据](#查询患者最新指标数据)
- [PatientRecordController 患者记录控制器](#patientrecordcontroller-患者记录控制器)
  - [查询患者记录](#查询患者记录)
- [数据模型定义](#数据模型定义)
- [业务流程说明](#业务流程说明)
- [安全性说明](#安全性说明)
- [性能指标](#性能指标)
- [常见问题](#常见问题)

## 简介

本文档详细描述了聊天机器人系统中患者数据管理相关的HTTP API接口，包括指标数据查询和患者记录查询功能。这些接口主要用于：

1. **指标数据管理**: 提供患者健康指标数据的查询功能，支持根据患者ID和指标key列表获取最新的指标数据
2. **患者记录查询**: 提供灵活的SQL查询功能，支持从OLAP数据库中获取患者相关记录数据

### 业务背景
- 系统需要为聊天机器人提供患者健康数据查询能力
- 支持实时获取患者最新的健康指标数据
- 提供灵活的数据查询接口，满足不同业务场景的数据需求
- 确保数据查询的安全性和性能

### 技术架构
- **框架**: Spring Boot + Spring MVC
- **数据访问**: Feign Client + 外部API调用
- **参数校验**: JSR-303 Bean Validation
- **日志记录**: SLF4J + Logback
- **响应格式**: 统一的Result包装器

## 接口概览

| 控制器 | 接口路径 | 请求方式 | 功能描述 | 负责人 |
|--------|----------|----------|----------|--------|
| IndicationDataController | `/indication/query-last` | POST | 查询患者最新指标数据 | Claude |
| PatientRecordController | `/patient/record/query` | POST | 查询患者记录 | Claude |

## 通用规范

### 请求格式

所有接口均采用HTTP协议，支持以下通用规范：

#### 请求头要求
```http
Content-Type: application/json
Accept: application/json
```

#### 字符编码
- 统一使用UTF-8编码
- 请求和响应数据均为JSON格式

### 响应格式

所有接口均返回统一的响应格式：

```json
{
  "data": "T",
  "code": "Integer",
  "message": "String"
}
```

#### 响应字段说明

| 字段名 | 类型 | 描述 |
|--------|------|------|
| data | T | 响应数据，泛型类型，根据具体接口而定 |
| code | Integer | 状态码，0表示成功，非0表示失败 |
| message | String | 状态描述信息 |

### 错误码规范

| 错误码 | 描述 | 处理建议 |
|--------|------|----------|
| 0 | 成功 | 正常处理返回数据 |
| -1 | 通用失败 | 检查请求参数和系统状态 |
| 400 | 参数校验失败 | 检查请求参数格式和必填项 |
| 500 | 系统内部错误 | 联系技术支持 |

### 参数校验规范

系统采用JSR-303 Bean Validation进行参数校验：

- `@NotBlank`: 字符串不能为空且不能只包含空白字符
- `@NotEmpty`: 集合不能为空
- `@Valid`: 启用嵌套对象校验

校验失败时返回400错误码，message字段包含具体的校验错误信息。
