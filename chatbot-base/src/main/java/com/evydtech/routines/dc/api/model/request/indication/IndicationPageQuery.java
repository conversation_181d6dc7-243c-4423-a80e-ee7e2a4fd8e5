/**
 * IndicationPageQuery
 * 
 * 指标数据分页查询请求对象
 * 兼容 routines-dc-api 的请求格式
 *
 * @author: <PERSON>
 * @date: 2025/7/29
 * @license: Copyright © 2012 - 2025 YiDuCloud.EVYD
 */
package com.evydtech.routines.dc.api.model.request.indication;

import lombok.Data;
import java.util.List;

@Data
public class IndicationPageQuery {
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 指标key列表
     */
    private List<String> keys;
}
