/**
 * FollowupTaskVo
 *
 * @author: 崔晓波
 * @email: <EMAIL>
 * @date: 2023/5/16 19:35
 * @license: Copyright © 2012 - 2023 YiDuCloud.EVYD
 */
package com.evydtech.chatbot.followup.domain.vo;

import cn.hutool.core.util.ObjectUtil;
import com.evydtech.chatbot.followup.entity.FollowupQuestionEntity;
import lombok.Data;

import java.io.Serializable;

@Data
public class FollowupQuestionVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * BizId
     */
    private String bizId;

    /**
     * UserBizId
     */
    private String userBizId;

    /**
     * No
     */
    private Integer no;

    /**
     * Title
     */
    private String title;

    /**
     * SubTitle
     */
    private String subTitle;

    /**
     * JumpUrl
     */
    private String jumpUrl;

    /**
     * Type
     */
    private Integer type;

    /**
     * Detail
     */
    private String detail;

    /**
     * StartTime
     */
    private Long startTime;

    /**
     * EndTime
     */
    private Long endTime;

    /**
     * Status
     */
    private Integer status;

    /**
     * ExtendData
     */
    private FollowupQuestionEntity.ExtendData extendData;

    /**
     * DataCheckStatus
     * 1: Enable 2: Unable
     */
    private Integer dataCheckStatus;

    /**
     * createdAt
     */
    private Long createdAt;

    /**
     * updatedAt
     */
    private Long updatedAt;

    /**
     * CheckDataValid
     *
     * @return Boolean
     */
    public Boolean checkDataValid() {
        return ObjectUtil.isNotNull(this.getBizId());
    }
}
