/**
 * FollowupTaskDao
 *
 * @author: 崔晓波
 * @email: <EMAIL>
 * @date: 2023/5/16 20:24
 * @license: Copyright © 2012 - 2023 YiDuCloud.EVYD
 */
package com.evydtech.chatbot.followup.dao;

import com.evydtech.chatbot.common.enums.DataCheckStatusEnum;
import com.evydtech.chatbot.followup.constant.SchemaFieldFollowupTaskConstant;
import com.evydtech.chatbot.followup.entity.FollowupQuestionEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class FollowupQuestionDao {
    @Autowired
    MongoTemplate mongoTemplate;

    /**
     * FindOneByUserBizIdAndType
     *
     * @param userBizId String
     * @param type      String
     * @return FollowupTaskEntity
     */
    public FollowupQuestionEntity findOneByUserBizIdAndType(String userBizId, Integer type) {
        Query query = new Query();
        Criteria criteria = new Criteria();
        criteria.and(SchemaFieldFollowupTaskConstant.USER_BIZ_ID).is(userBizId);
        criteria.and(SchemaFieldFollowupTaskConstant.TYPE).is(type);
        criteria.and(SchemaFieldFollowupTaskConstant.DATA_CHECK_STATUS).is(DataCheckStatusEnum.ENABLE.getCode());
        query.addCriteria(criteria);
        return this.mongoTemplate.findOne(query, FollowupQuestionEntity.class);
    }

    /**
     * FindOneByType
     *
     * @param type      String
     * @return FollowupTaskEntity
     */
    public FollowupQuestionEntity findOneByType(Integer type) {
        Query query = new Query();
        Criteria criteria = new Criteria();
        criteria.and(SchemaFieldFollowupTaskConstant.TYPE).is(type);
        criteria.and(SchemaFieldFollowupTaskConstant.DATA_CHECK_STATUS).is(DataCheckStatusEnum.ENABLE.getCode());
        query.addCriteria(criteria);
        return this.mongoTemplate.findOne(query, FollowupQuestionEntity.class);
    }

    /**
     * FindOneByType
     *
     * @param type      String
     * @return FollowupTaskEntity
     */
    public List<FollowupQuestionEntity> findListByType(Integer type) {
        Query query = new Query();
        Criteria criteria = new Criteria();
        criteria.and(SchemaFieldFollowupTaskConstant.TYPE).is(type);
        criteria.and(SchemaFieldFollowupTaskConstant.DATA_CHECK_STATUS).is(DataCheckStatusEnum.ENABLE.getCode());
        query.addCriteria(criteria);
        return this.mongoTemplate.find(query, FollowupQuestionEntity.class);
    }

    /**
     * FindOneByType
     *
     * @param type      String
     * @return FollowupTaskEntity
     */
    public FollowupQuestionEntity findListByTypeAndNo(Integer type, Integer no) {
        Query query = new Query();
        Criteria criteria = new Criteria();
        criteria.and(SchemaFieldFollowupTaskConstant.TYPE).is(type);
        criteria.and(SchemaFieldFollowupTaskConstant.NO).is(no);
        criteria.and(SchemaFieldFollowupTaskConstant.DATA_CHECK_STATUS).is(DataCheckStatusEnum.ENABLE.getCode());
        query.addCriteria(criteria);
        return this.mongoTemplate.findOne(query, FollowupQuestionEntity.class);
    }
}
