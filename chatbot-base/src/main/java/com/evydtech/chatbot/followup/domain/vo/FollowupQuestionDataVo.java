/**
 * FollowupTaskVo
 *
 * @author: 崔晓波
 * @email: <EMAIL>
 * @date: 2023/5/16 19:35
 * @license: Copyright © 2012 - 2023 YiDuCloud.EVYD
 */
package com.evydtech.chatbot.followup.domain.vo;

import cn.hutool.core.util.ObjectUtil;
import com.evydtech.chatbot.followup.entity.FollowupQuestionDataEntity;
import lombok.Data;

import java.io.Serializable;

@Data
public class FollowupQuestionDataVo implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * BizId
     */
    private String bizId;

    /**
     * UserBizId
     */
    private String userBizId;

    /**
     * QuestionBizId
     */
    private String questionBizId;

    /**
     * Followup type
     * @see com.evydtech.chatbot.followup.enums.FollowupTypeEnum
     */
    private Integer type;

    /**
     * No
     */
    private Integer no;

    /**
     * Title
     */
    private String title;

    /**
     * Result
     */
    private String result;

    /**
     * Status
     * 1. UnFinished 2. Finished
     */
    private Integer status;

    /**
     * ExtendData
     */
    private FollowupQuestionDataEntity.ExtendData extendData;

    /**
     * DataCheckStatus
     * 1: Enable 2: Unable
     */
    private Integer dataCheckStatus;

    /**
     * createdAt
     */
    private Long createdAt;

    /**
     * updatedAt
     */
    private Long updatedAt;

    /**
     * engagedAt
     */
    private Long engagedAt;


    /**
     * CheckDataValid
     *
     * @return Boolean
     */
    public Boolean checkDataValid() {
        return ObjectUtil.isNotNull(this.getBizId());
    }
}
