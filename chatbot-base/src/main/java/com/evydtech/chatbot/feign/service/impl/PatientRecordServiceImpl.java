/**
 * PatientRecordServiceImpl
 * 
 * 患者记录服务实现类
 * 提供患者记录查询的核心业务逻辑实现
 * 通过Feign客户端调用远程OLAP查询服务
 *
 * @author: Claude
 * @date: 2025/7/29
 * @license: Copyright © 2012 - 2025 YiDuCloud.EVYD
 */
package com.evydtech.chatbot.feign.service.impl;

import com.evydtech.chatbot.feign.PatientRecordFeignClient;
import com.evydtech.chatbot.feign.domain.PatientRecordQueryQo;
import com.evydtech.chatbot.feign.domain.PatientRecordQueryVo;
import java.util.Map;
import com.evydtech.chatbot.feign.service.PatientRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class PatientRecordServiceImpl implements PatientRecordService {

    @Autowired
    private PatientRecordFeignClient patientRecordFeignClient;

    /**
     * 查询患者记录
     * 
     * @param sql 要执行的SQL查询语句
     * @return 包含查询结果的响应对象，其中data字段为Map列表，每个Map代表一行查询结果
     */
    @Override
    public PatientRecordQueryVo<Map<String, Object>> queryPatientRecords(String sql) {
        PatientRecordQueryQo queryQo = new PatientRecordQueryQo();
        queryQo.setSqlVal(sql);
        return patientRecordFeignClient.queryPost(queryQo);
    }
}