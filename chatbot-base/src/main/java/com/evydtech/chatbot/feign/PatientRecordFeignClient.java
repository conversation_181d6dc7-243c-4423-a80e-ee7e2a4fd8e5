/**
 * PatientRecordFeignClient
 * 
 * Feign客户端接口，用于调用患者记录查询服务。
 * 该接口通过HTTP POST方式向OLAP查询服务发送SQL查询请求，
 * 并获取患者就诊记录数据。
 *
 * @author: Claude
 * @date: 2025/7/29
 * @license: Copyright © 2012 - 2025 YiDuCloud.EVYD
 */
package com.evydtech.chatbot.feign;

import com.evydtech.chatbot.feign.domain.PatientRecordQueryQo;
import com.evydtech.chatbot.feign.domain.PatientRecordQueryVo;
import java.util.Map;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = "PatientRecordFeignClient", url = "http://evyd-health-service-patient-record-query")
public interface PatientRecordFeignClient {
    
    /**
     * 执行患者记录查询
     * 
     * @param request 包含SQL查询条件的请求对象
     * @return 包含查询结果的响应对象，其中data字段为OlapVisitRecord列表
     */
    @PostMapping("/internal/olap/query-post")
    PatientRecordQueryVo<Map<String, Object>> queryPost(@RequestBody PatientRecordQueryQo request);
}