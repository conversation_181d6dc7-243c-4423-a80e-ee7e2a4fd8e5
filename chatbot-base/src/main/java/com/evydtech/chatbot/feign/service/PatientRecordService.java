/**
 * PatientRecordService
 * 
 * 患者记录查询服务接口
 * 提供患者记录查询的核心业务逻辑定义
 * 支持通过SQL查询获取患者相关数据
 *
 * @author: Claude
 * @date: 2025/7/29
 * @license: Copyright © 2012 - 2025 YiDuCloud.EVYD
 */
package com.evydtech.chatbot.feign.service;

import com.evydtech.chatbot.feign.domain.PatientRecordQueryVo;

import java.util.Map;

public interface PatientRecordService {
    
    /**
     * 查询患者记录
     * 
     * 使用通用Map结构接收查询结果，因为SQL查询返回的列结构不确定，
     * 使用Map可以灵活适配各种查询结果格式
     * 
     * @param sql 要执行的SQL查询语句
     * @return 包含查询结果的响应对象，其中data字段为Map列表，
     *         每个Map代表一行查询结果，key为列名，value为列值
     */
    PatientRecordQueryVo<Map<String, Object>> queryPatientRecords(String sql);
}