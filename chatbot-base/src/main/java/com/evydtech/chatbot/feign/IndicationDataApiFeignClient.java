/**
 * IndicationDataApiFeignClient
 *
 * 指标数据API的Feign客户端实现
 * 直接继承IndicationDataApi接口，提供远程调用能力
 *
 * @author: Claude
 * @date: 2025/7/29
 * @license: Copyright © 2012 - 2025 YiDuCloud.EVYD
 */
package com.evydtech.chatbot.feign;

import com.evydtech.routines.dc.api.model.request.indication.IndicationPageQuery;
import com.evydtech.routines.dc.api.model.response.indication.IndicationPageQueryResult;
import com.evydtech.routines.dc.api.web.IndicationDataApi;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = "IndicationDataApi", url = "${evyd.micro-service.hosts.routines:http://evyd-health-manage-routines}")
public interface IndicationDataApiFeignClient extends IndicationDataApi {

    /**
     * 查询患者最新指标数据
     * 重新声明方法并添加HTTP映射注解
     *
     * @param request 指标查询请求参数
     * @return 指标查询结果
     */
    @Override
    @PostMapping("/internal/indication/query-last")
    IndicationPageQueryResult queryLast(@RequestBody IndicationPageQuery request);
}
