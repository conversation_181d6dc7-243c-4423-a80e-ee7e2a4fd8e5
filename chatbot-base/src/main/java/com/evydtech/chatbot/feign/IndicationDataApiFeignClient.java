/**
 * IndicationDataApiFeignClient
 *
 * 指标数据API的Feign客户端实现
 * 直接继承IndicationDataApi接口，复用父接口的所有方法
 *
 * @author: <PERSON>
 * @date: 2025/7/29
 * @license: Copyright © 2012 - 2025 YiDuCloud.EVYD
 */
package com.evydtech.chatbot.feign;

import com.evydtech.routines.dc.api.web.IndicationDataApi;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(value = "IndicationDataApi", url = "${evyd.micro-service.hosts.routines:http://evyd-health-manage-routines}")
public interface IndicationDataApiFeignClient extends IndicationDataApi {
    // 直接继承IndicationDataApi的所有方法，Feign会自动实现远程调用
}
