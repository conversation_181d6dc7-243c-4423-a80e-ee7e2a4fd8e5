/**
 * IndicationQueryQo
 * 
 * 指标数据查询请求对象
 * 用于封装患者指标数据查询请求参数
 *
 * @author: <PERSON>
 * @date: 2025/7/29
 * @license: Copyright © 2012 - 2025 YiDuCloud.EVYD
 */
package com.evydtech.chatbot.business.pojo.qo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
public class IndicationQueryQo {
    /**
     * 患者memberId
     */
    @NotBlank(message = "患者userId不能为空")
    private String userId;
    
    /**
     * 指标key列表
     */
    @NotEmpty(message = "指标key列表不能为空")
    private List<String> keys;
}